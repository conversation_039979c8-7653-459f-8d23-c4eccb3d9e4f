"""
深度爬取引擎 - 个股和创作者深度优化
"""
import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import re

from crawler_engine import SmartCrawler, CrawlResult
from deep_crawler_config import (
    DeepCrawlConfig, DeepScrollStrategy, ContentExtractor, 
    PriorityManager, CrawlDepth, ContentPriority
)

logger = logging.getLogger(__name__)

class DeepCrawlerEngine:
    """深度爬取引擎"""
    
    def __init__(self, config: DeepCrawlConfig):
        self.config = config
        self.crawler = SmartCrawler(enable_anti_detection=True)
        self.priority_manager = PriorityManager(config)
        self.content_extractor = ContentExtractor()
        
        # 初始化优先级股票和创作者
        self._initialize_priorities()
        
        logger.info("深度爬取引擎初始化完成")
    
    def _initialize_priorities(self):
        """初始化优先级设置"""
        # 设置高优先级股票（示例）
        high_priority_stocks = [
            "SH000001", "SZ399001", "SH000300",  # 指数
            "SH600036", "SH600519", "SZ000858",  # 大盘蓝筹
            "SZ300750", "SH688981", "SZ002415"   # 科技股
        ]
        
        for stock in high_priority_stocks:
            self.priority_manager.set_stock_priority(stock, ContentPriority.HIGH)
        
        # 设置关键优先级股票
        critical_stocks = self.config.priority_stocks
        for stock in critical_stocks:
            self.priority_manager.set_stock_priority(stock, ContentPriority.CRITICAL)
    
    async def deep_crawl_stock(self, symbol: str, **kwargs) -> Dict[str, Any]:
        """深度爬取个股信息"""
        logger.info(f"开始深度爬取股票: {symbol}")
        
        # 获取该股票的爬取配置
        crawl_config = self.priority_manager.get_stock_crawl_config(symbol)
        
        try:
            # 1. 基础股票信息
            basic_result = await self._crawl_stock_basic_info(symbol, crawl_config)
            
            # 2. 深度评论数据
            comments_result = await self._crawl_stock_comments_deep(symbol, crawl_config)
            
            # 3. 相关分析文章（如果启用）
            analysis_result = {}
            if crawl_config.get("include_analysis", False):
                analysis_result = await self._crawl_stock_analysis(symbol, crawl_config)
            
            # 4. 相关新闻（如果启用）
            news_result = {}
            if crawl_config.get("include_news", False):
                news_result = await self._crawl_stock_news(symbol, crawl_config)
            
            # 5. 历史数据（如果启用）
            historical_result = {}
            if crawl_config.get("include_historical", False):
                historical_result = await self._crawl_stock_historical(symbol, crawl_config)
            
            # 合并结果
            deep_result = {
                "success": True,
                "symbol": symbol,
                "crawl_time": datetime.now().isoformat(),
                "crawl_depth": crawl_config.get("crawl_depth", CrawlDepth.DEEP).value,
                "basic_info": basic_result,
                "comments": comments_result,
                "analysis": analysis_result,
                "news": news_result,
                "historical": historical_result,
                "total_data_points": self._count_data_points(basic_result, comments_result, analysis_result, news_result)
            }
            
            logger.info(f"股票 {symbol} 深度爬取完成，数据点: {deep_result['total_data_points']}")
            return deep_result
            
        except Exception as e:
            logger.error(f"股票 {symbol} 深度爬取失败: {e}")
            return {
                "success": False,
                "symbol": symbol,
                "error": str(e),
                "crawl_time": datetime.now().isoformat()
            }
    
    async def _crawl_stock_basic_info(self, symbol: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取股票基础信息"""
        url = f"https://xueqiu.com/S/{symbol}"
        
        # 使用深度滚动策略
        scroll_strategy = DeepScrollStrategy.get_strategy_for_content_type("stock", config.get("crawl_depth", CrawlDepth.DEEP))
        
        result = await self.crawler.crawl_url(url)
        
        if result.success:
            # 使用深度选择器提取信息
            selectors = self.content_extractor.get_selectors_for_content("stock")
            extracted_data = self._extract_data_with_selectors(result.raw_html, selectors)
            
            return {
                "success": True,
                "basic_info": extracted_data.get("basic_info", {}),
                "detailed_info": extracted_data.get("detailed_info", {}),
                "response_time": result.response_time
            }
        else:
            return {"success": False, "error": result.error}
    
    async def _crawl_stock_comments_deep(self, symbol: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """深度爬取股票评论"""
        url = f"https://xueqiu.com/S/{symbol}"
        max_comments = config.get("max_comments", 200)
        
        # 生成深度评论爬取的JavaScript
        deep_scroll_js = self._generate_deep_comment_scroll_js(max_comments)
        
        result = await self.crawler.crawl_url(url)
        
        if result.success:
            # 提取评论数据
            comments_data = self._extract_comments_deep(result.raw_html)
            
            # 分析评论情感
            for comment in comments_data:
                comment["sentiment_analysis"] = self._analyze_comment_sentiment(comment.get("text", ""))
                comment["mentioned_stocks"] = self._extract_mentioned_stocks(comment.get("text", ""))
            
            return {
                "success": True,
                "comments": comments_data,
                "total_comments": len(comments_data),
                "sentiment_summary": self._summarize_sentiment(comments_data)
            }
        else:
            return {"success": False, "error": result.error}
    
    async def _crawl_stock_analysis(self, symbol: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取股票分析文章"""
        # 搜索相关分析文章
        search_url = f"https://xueqiu.com/search?q={symbol}&type=article"
        
        result = await self.crawler.crawl_url(search_url)
        
        if result.success:
            analysis_data = self._extract_analysis_articles(result.raw_html, symbol)
            return {
                "success": True,
                "analysis_articles": analysis_data,
                "total_articles": len(analysis_data)
            }
        else:
            return {"success": False, "error": result.error}
    
    async def _crawl_stock_news(self, symbol: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取股票相关新闻"""
        # 搜索相关新闻
        search_url = f"https://xueqiu.com/search?q={symbol}&type=news"
        
        result = await self.crawler.crawl_url(search_url)
        
        if result.success:
            news_data = self._extract_news_articles(result.raw_html, symbol)
            return {
                "success": True,
                "news_articles": news_data,
                "total_news": len(news_data)
            }
        else:
            return {"success": False, "error": result.error}
    
    async def deep_crawl_creator(self, creator_id: str, **kwargs) -> Dict[str, Any]:
        """深度爬取创作者信息"""
        logger.info(f"开始深度爬取创作者: {creator_id}")
        
        # 获取该创作者的爬取配置
        crawl_config = self.priority_manager.get_creator_crawl_config(creator_id)
        
        try:
            # 1. 创作者基础信息
            profile_result = await self._crawl_creator_profile(creator_id, crawl_config)
            
            # 2. 创作者文章列表
            posts_result = await self._crawl_creator_posts_deep(creator_id, crawl_config)
            
            # 3. 粉丝和互动数据（如果启用）
            engagement_result = {}
            if crawl_config.get("include_followers", False):
                engagement_result = await self._crawl_creator_engagement(creator_id, crawl_config)
            
            # 4. 历史文章（如果启用）
            history_result = {}
            if crawl_config.get("include_history", False):
                history_result = await self._crawl_creator_history(creator_id, crawl_config)
            
            # 合并结果
            deep_result = {
                "success": True,
                "creator_id": creator_id,
                "crawl_time": datetime.now().isoformat(),
                "crawl_depth": crawl_config.get("crawl_depth", CrawlDepth.DEEP).value,
                "profile": profile_result,
                "posts": posts_result,
                "engagement": engagement_result,
                "history": history_result,
                "total_posts": len(posts_result.get("posts", [])),
                "influence_score": self._calculate_influence_score(profile_result, posts_result, engagement_result)
            }
            
            logger.info(f"创作者 {creator_id} 深度爬取完成，文章数: {deep_result['total_posts']}")
            return deep_result
            
        except Exception as e:
            logger.error(f"创作者 {creator_id} 深度爬取失败: {e}")
            return {
                "success": False,
                "creator_id": creator_id,
                "error": str(e),
                "crawl_time": datetime.now().isoformat()
            }
    
    async def _crawl_creator_profile(self, creator_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取创作者档案信息"""
        url = f"https://xueqiu.com/u/{creator_id}"
        
        result = await self.crawler.crawl_url(url)
        
        if result.success:
            # 使用深度选择器提取创作者信息
            selectors = self.content_extractor.get_selectors_for_content("creator", "profile")
            profile_data = self._extract_data_with_selectors(result.raw_html, selectors)
            
            return {
                "success": True,
                "profile_data": profile_data,
                "response_time": result.response_time
            }
        else:
            return {"success": False, "error": result.error}
    
    async def _crawl_creator_posts_deep(self, creator_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """深度爬取创作者文章"""
        url = f"https://xueqiu.com/u/{creator_id}"
        max_posts = config.get("max_posts", 100)
        
        # 生成深度文章爬取的JavaScript
        deep_scroll_js = self._generate_deep_posts_scroll_js(max_posts)
        
        result = await self.crawler.crawl_url(url)
        
        if result.success:
            # 提取文章数据
            posts_data = self._extract_posts_deep(result.raw_html)
            
            # 分析文章内容
            for post in posts_data:
                post["content_analysis"] = self._analyze_post_content(post.get("content", ""))
                post["mentioned_stocks"] = self._extract_mentioned_stocks(post.get("content", ""))
                post["investment_sentiment"] = self._analyze_investment_sentiment(post.get("content", ""))
            
            return {
                "success": True,
                "posts": posts_data,
                "total_posts": len(posts_data),
                "content_summary": self._summarize_creator_content(posts_data)
            }
        else:
            return {"success": False, "error": result.error}
    
    def _generate_deep_comment_scroll_js(self, max_comments: int) -> str:
        """生成深度评论爬取的JavaScript代码"""
        return f"""
        (async function() {{
            console.log('🔍 开始深度评论爬取，目标: {max_comments} 条评论');
            
            let totalComments = 0;
            let scrollAttempts = 0;
            const maxScrollAttempts = 20;
            let lastHeight = 0;
            
            // 展开所有折叠的评论
            function expandAllComments() {{
                const expandButtons = document.querySelectorAll(
                    '[class*="expand"], [class*="more"], [class*="展开"], .unfold, .show-more'
                );
                let expanded = 0;
                expandButtons.forEach(btn => {{
                    if (btn.offsetParent !== null && !btn.dataset.clicked) {{
                        btn.click();
                        btn.dataset.clicked = 'true';
                        expanded++;
                    }}
                }});
                return expanded;
            }}
            
            // 点击加载更多评论
            function clickLoadMoreComments() {{
                const loadMoreButtons = document.querySelectorAll(
                    '[class*="load-more"], [class*="加载更多"], .load-more-comments, .more-comments'
                );
                let clicked = false;
                loadMoreButtons.forEach(btn => {{
                    if (btn.offsetParent !== null && !btn.dataset.clicked) {{
                        btn.click();
                        btn.dataset.clicked = 'true';
                        clicked = true;
                    }}
                }});
                return clicked;
            }}
            
            // 获取当前评论数量
            function getCommentCount() {{
                const comments = document.querySelectorAll(
                    '.comment, [class*="comment"], .timeline-item, [class*="timeline"]'
                );
                return comments.length;
            }}
            
            while (scrollAttempts < maxScrollAttempts && totalComments < {max_comments}) {{
                // 展开评论
                const expanded = expandAllComments();
                if (expanded > 0) {{
                    console.log(`📖 展开了 ${{expanded}} 个评论`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }}
                
                // 点击加载更多
                const loadedMore = clickLoadMoreComments();
                if (loadedMore) {{
                    console.log('🔄 点击了加载更多按钮');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }}
                
                // 滚动到底部
                const currentHeight = document.body.scrollHeight;
                window.scrollTo(0, currentHeight);
                await new Promise(resolve => setTimeout(resolve, 2500));
                
                // 检查是否有新内容
                const newHeight = document.body.scrollHeight;
                totalComments = getCommentCount();
                
                console.log(`📊 滚动 ${{scrollAttempts + 1}}: ${{totalComments}} 条评论`);
                
                if (newHeight === lastHeight && !loadedMore && expanded === 0) {{
                    console.log('📄 没有新内容，停止滚动');
                    break;
                }}
                
                lastHeight = newHeight;
                scrollAttempts++;
            }}
            
            console.log(`✅ 深度评论爬取完成: ${{totalComments}} 条评论`);
            return totalComments;
        }})();
        """
    
    def _generate_deep_posts_scroll_js(self, max_posts: int) -> str:
        """生成深度文章爬取的JavaScript代码"""
        return f"""
        (async function() {{
            console.log('📝 开始深度文章爬取，目标: {max_posts} 篇文章');
            
            let totalPosts = 0;
            let scrollAttempts = 0;
            const maxScrollAttempts = 15;
            let lastHeight = 0;
            
            // 展开所有文章内容
            function expandAllPosts() {{
                const expandButtons = document.querySelectorAll(
                    '[class*="expand"], [class*="unfold"], [class*="展开全文"], .show-full'
                );
                let expanded = 0;
                expandButtons.forEach(btn => {{
                    if (btn.offsetParent !== null && !btn.dataset.clicked) {{
                        btn.click();
                        btn.dataset.clicked = 'true';
                        expanded++;
                    }}
                }});
                return expanded;
            }}
            
            // 获取当前文章数量
            function getPostCount() {{
                const posts = document.querySelectorAll(
                    '.timeline-item, [class*="post"], [class*="article"], .feed-item'
                );
                return posts.length;
            }}
            
            while (scrollAttempts < maxScrollAttempts && totalPosts < {max_posts}) {{
                // 展开文章内容
                const expanded = expandAllPosts();
                if (expanded > 0) {{
                    console.log(`📖 展开了 ${{expanded}} 篇文章`);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }}
                
                // 渐进式滚动
                const scrollPositions = [0.3, 0.6, 0.9, 1.0];
                for (let position of scrollPositions) {{
                    const targetY = document.body.scrollHeight * position;
                    window.scrollTo(0, targetY);
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    // 在每个位置都尝试展开内容
                    expandAllPosts();
                }}
                
                // 检查是否有新内容
                const newHeight = document.body.scrollHeight;
                totalPosts = getPostCount();
                
                console.log(`📊 滚动 ${{scrollAttempts + 1}}: ${{totalPosts}} 篇文章`);
                
                if (newHeight === lastHeight && expanded === 0) {{
                    console.log('📄 没有新内容，停止滚动');
                    break;
                }}
                
                lastHeight = newHeight;
                scrollAttempts++;
            }}
            
            console.log(`✅ 深度文章爬取完成: ${{totalPosts}} 篇文章`);
            return totalPosts;
        }})();
        """

    def _extract_data_with_selectors(self, html: str, selectors: Dict[str, Any]) -> Dict[str, Any]:
        """使用选择器提取数据"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html, 'html.parser')
        extracted_data = {}

        for category, category_selectors in selectors.items():
            extracted_data[category] = {}

            for field, field_selectors in category_selectors.items():
                value = None

                # 尝试每个选择器直到找到数据
                for selector in field_selectors:
                    try:
                        element = soup.select_one(selector)
                        if element:
                            # 根据元素类型提取不同的值
                            if element.get('data-value'):
                                value = element.get('data-value')
                            elif element.get('href'):
                                value = element.get('href')
                            else:
                                value = element.get_text(strip=True)

                            if value:
                                break
                    except Exception as e:
                        logger.debug(f"选择器 {selector} 提取失败: {e}")
                        continue

                extracted_data[category][field] = value

        return extracted_data

    def _extract_comments_deep(self, html: str) -> List[Dict[str, Any]]:
        """深度提取评论数据"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html, 'html.parser')
        comments = []

        # 多种评论选择器
        comment_selectors = [
            '.comment', '[class*="comment"]', '.timeline-item',
            '[class*="timeline"]', '.feed-item', '[class*="feed"]'
        ]

        for selector in comment_selectors:
            comment_elements = soup.select(selector)
            if comment_elements:
                break

        for element in comment_elements:
            try:
                comment_data = {
                    "id": element.get('data-id', ''),
                    "text": self._extract_comment_text(element),
                    "author": self._extract_comment_author(element),
                    "author_id": self._extract_comment_author_id(element),
                    "time": self._extract_comment_time(element),
                    "likes": self._extract_comment_likes(element),
                    "replies": self._extract_comment_replies(element),
                    "is_reply": self._is_reply_comment(element),
                    "reply_to": self._extract_reply_to(element)
                }

                # 只添加有内容的评论
                if comment_data["text"]:
                    comments.append(comment_data)

            except Exception as e:
                logger.debug(f"提取评论失败: {e}")
                continue

        return comments

    def _extract_posts_deep(self, html: str) -> List[Dict[str, Any]]:
        """深度提取文章数据"""
        from bs4 import BeautifulSoup

        soup = BeautifulSoup(html, 'html.parser')
        posts = []

        # 多种文章选择器
        post_selectors = [
            '.timeline-item', '[class*="post"]', '[class*="article"]',
            '.feed-item', '[class*="feed"]', '[class*="status"]'
        ]

        for selector in post_selectors:
            post_elements = soup.select(selector)
            if post_elements:
                break

        for element in post_elements:
            try:
                post_data = {
                    "id": element.get('data-id', ''),
                    "title": self._extract_post_title(element),
                    "content": self._extract_post_content(element),
                    "author": self._extract_post_author(element),
                    "time": self._extract_post_time(element),
                    "likes": self._extract_post_likes(element),
                    "comments": self._extract_post_comment_count(element),
                    "shares": self._extract_post_shares(element),
                    "tags": self._extract_post_tags(element),
                    "url": self._extract_post_url(element),
                    "type": self._determine_post_type(element)
                }

                # 只添加有内容的文章
                if post_data["content"] or post_data["title"]:
                    posts.append(post_data)

            except Exception as e:
                logger.debug(f"提取文章失败: {e}")
                continue

        return posts

    def _extract_comment_text(self, element) -> str:
        """提取评论文本"""
        text_selectors = [
            '.comment-content', '.text', '.content',
            '[class*="content"]', '[class*="text"]'
        ]

        for selector in text_selectors:
            text_element = element.select_one(selector)
            if text_element:
                return text_element.get_text(strip=True)

        # 如果没有找到特定选择器，返回元素的文本内容
        return element.get_text(strip=True)[:500]  # 限制长度

    def _extract_comment_author(self, element) -> str:
        """提取评论作者"""
        author_selectors = [
            '.author', '.user-name', '[data-author]',
            '[class*="author"]', '[class*="user"]'
        ]

        for selector in author_selectors:
            author_element = element.select_one(selector)
            if author_element:
                return author_element.get_text(strip=True)

        return ""

    def _analyze_comment_sentiment(self, text: str) -> Dict[str, Any]:
        """分析评论情感"""
        if not text:
            return {"sentiment": "neutral", "confidence": 0.0}

        # 简单的情感分析
        positive_words = ["好", "涨", "牛", "买", "推荐", "看好", "利好", "上涨", "强势", "突破"]
        negative_words = ["差", "跌", "熊", "卖", "看空", "利空", "下跌", "弱势", "破位", "风险"]

        positive_count = sum(1 for word in positive_words if word in text)
        negative_count = sum(1 for word in negative_words if word in text)

        if positive_count > negative_count:
            sentiment = "positive"
            confidence = min(positive_count / (positive_count + negative_count + 1), 0.9)
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = min(negative_count / (positive_count + negative_count + 1), 0.9)
        else:
            sentiment = "neutral"
            confidence = 0.5

        return {
            "sentiment": sentiment,
            "confidence": confidence,
            "positive_signals": positive_count,
            "negative_signals": negative_count
        }

    def _extract_mentioned_stocks(self, text: str) -> List[str]:
        """提取文本中提到的股票代码"""
        if not text:
            return []

        # 股票代码模式
        patterns = [
            r'[SZ|SH]\d{6}',  # 标准格式
            r'\$([A-Z]{2,5})\$',  # 雪球格式
            r'(\d{6})',  # 纯数字
        ]

        mentioned_stocks = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            mentioned_stocks.extend(matches)

        return list(set(mentioned_stocks))

    def _summarize_sentiment(self, comments_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """汇总评论情感"""
        if not comments_data:
            return {"positive": 0, "negative": 0, "neutral": 0, "overall": "neutral"}

        sentiment_counts = {"positive": 0, "negative": 0, "neutral": 0}

        for comment in comments_data:
            sentiment_analysis = comment.get("sentiment_analysis", {})
            sentiment = sentiment_analysis.get("sentiment", "neutral")
            sentiment_counts[sentiment] += 1

        total = len(comments_data)
        sentiment_percentages = {
            k: round(v / total * 100, 2) for k, v in sentiment_counts.items()
        }

        # 确定整体情感
        if sentiment_percentages["positive"] > sentiment_percentages["negative"]:
            overall = "positive"
        elif sentiment_percentages["negative"] > sentiment_percentages["positive"]:
            overall = "negative"
        else:
            overall = "neutral"

        return {
            "counts": sentiment_counts,
            "percentages": sentiment_percentages,
            "overall": overall,
            "total_comments": total
        }

    def _count_data_points(self, *results) -> int:
        """计算数据点总数"""
        total = 0
        for result in results:
            if isinstance(result, dict):
                if "comments" in result:
                    total += len(result["comments"])
                if "posts" in result:
                    total += len(result["posts"])
                if "articles" in result:
                    total += len(result["articles"])
                # 递归计算嵌套字典
                for value in result.values():
                    if isinstance(value, (list, dict)):
                        total += self._count_nested_items(value)
        return total

    def _count_nested_items(self, data) -> int:
        """递归计算嵌套数据项"""
        if isinstance(data, list):
            return len(data)
        elif isinstance(data, dict):
            return sum(1 for v in data.values() if v is not None and v != "")
        return 0

    def _extract_analysis_articles(self, html_content: str, symbol: str) -> List[Dict[str, Any]]:
        """从HTML中提取分析文章"""
        from bs4 import BeautifulSoup

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            articles = []

            # 查找文章链接和标题
            article_elements = soup.find_all(['a', 'div'], class_=lambda x: x and ('article' in x.lower() or 'post' in x.lower()))

            for element in article_elements[:10]:  # 限制数量
                try:
                    title = element.get_text(strip=True)
                    url = element.get('href', '')

                    if title and len(title) > 10:  # 过滤太短的标题
                        articles.append({
                            'title': title[:200],  # 限制标题长度
                            'url': url,
                            'symbol': symbol,
                            'type': 'analysis'
                        })
                except:
                    continue

            return articles

        except Exception as e:
            logger.warning(f"提取分析文章失败: {e}")
            return []

    def _extract_news_articles(self, html_content: str, symbol: str) -> List[Dict[str, Any]]:
        """从HTML中提取新闻文章"""
        from bs4 import BeautifulSoup

        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            news = []

            # 查找新闻链接和标题
            news_elements = soup.find_all(['a', 'div'], class_=lambda x: x and ('news' in x.lower() or 'article' in x.lower()))

            for element in news_elements[:10]:  # 限制数量
                try:
                    title = element.get_text(strip=True)
                    url = element.get('href', '')

                    if title and len(title) > 10:  # 过滤太短的标题
                        news.append({
                            'title': title[:200],  # 限制标题长度
                            'url': url,
                            'symbol': symbol,
                            'type': 'news'
                        })
                except:
                    continue

            return news

        except Exception as e:
            logger.warning(f"提取新闻文章失败: {e}")
            return []

    async def _crawl_stock_historical(self, symbol: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取股票历史数据"""
        try:
            # 简化实现，返回基本结构
            return {
                "success": True,
                "historical_data": [],
                "symbol": symbol,
                "note": "历史数据功能需要进一步实现"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _crawl_creator_engagement(self, creator_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取创作者互动数据"""
        try:
            # 简化实现，返回基本结构
            return {
                "success": True,
                "engagement_data": {
                    "avg_likes": 0,
                    "avg_comments": 0,
                    "follower_growth": 0
                },
                "creator_id": creator_id,
                "note": "互动数据功能需要进一步实现"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _crawl_creator_history(self, creator_id: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """爬取创作者历史数据"""
        try:
            # 简化实现，返回基本结构
            return {
                "success": True,
                "history_data": [],
                "creator_id": creator_id,
                "note": "历史数据功能需要进一步实现"
            }
        except Exception as e:
            return {"success": False, "error": str(e)}

    def _extract_comment_author_id(self, element) -> str:
        """提取评论作者ID"""
        author_id_selectors = [
            '[data-author-id]', '[data-user-id]',
            '[class*="user-id"]', '[class*="author-id"]'
        ]

        for selector in author_id_selectors:
            author_element = element.select_one(selector)
            if author_element:
                return author_element.get('data-author-id') or author_element.get('data-user-id', '')

        return ""

    def _extract_comment_time(self, element) -> str:
        """提取评论时间"""
        time_selectors = [
            '.time', '.timestamp', '[data-time]',
            '[class*="time"]', '[class*="date"]'
        ]

        for selector in time_selectors:
            time_element = element.select_one(selector)
            if time_element:
                return time_element.get_text(strip=True)

        return ""

    def _extract_comment_likes(self, element) -> int:
        """提取评论点赞数"""
        likes_selectors = [
            '.likes', '.fav-count', '[data-likes]',
            '[class*="like"]', '[class*="fav"]'
        ]

        for selector in likes_selectors:
            likes_element = element.select_one(selector)
            if likes_element:
                likes_text = likes_element.get_text(strip=True)
                try:
                    return int(''.join(filter(str.isdigit, likes_text)))
                except:
                    continue

        return 0

    def _extract_comment_replies(self, element) -> int:
        """提取评论回复数"""
        replies_selectors = [
            '.replies', '.reply-count', '[data-replies]',
            '[class*="reply"]', '[class*="comment-count"]'
        ]

        for selector in replies_selectors:
            replies_element = element.select_one(selector)
            if replies_element:
                replies_text = replies_element.get_text(strip=True)
                try:
                    return int(''.join(filter(str.isdigit, replies_text)))
                except:
                    continue

        return 0

    def _is_reply_comment(self, element) -> bool:
        """判断是否为回复评论"""
        reply_indicators = [
            'reply', 'response', '回复', 'comment-reply'
        ]

        class_names = ' '.join(element.get('class', []))
        return any(indicator in class_names.lower() for indicator in reply_indicators)

    def _extract_reply_to(self, element) -> str:
        """提取回复对象"""
        reply_to_selectors = [
            '[data-reply-to]', '.reply-to', '[class*="reply-to"]'
        ]

        for selector in reply_to_selectors:
            reply_element = element.select_one(selector)
            if reply_element:
                return reply_element.get_text(strip=True)

        return ""

    def _extract_post_title(self, element) -> str:
        """提取文章标题"""
        title_selectors = [
            '.title', '.post-title', 'h1', 'h2', 'h3',
            '[class*="title"]', '[class*="headline"]'
        ]

        for selector in title_selectors:
            title_element = element.select_one(selector)
            if title_element:
                return title_element.get_text(strip=True)

        return ""

    def _extract_post_content(self, element) -> str:
        """提取文章内容"""
        content_selectors = [
            '.content', '.post-content', '.article-content',
            '[class*="content"]', '[class*="text"]'
        ]

        for selector in content_selectors:
            content_element = element.select_one(selector)
            if content_element:
                return content_element.get_text(strip=True)[:1000]  # 限制长度

        # 如果没有找到特定选择器，返回元素的文本内容
        return element.get_text(strip=True)[:1000]

    def _extract_post_author(self, element) -> str:
        """提取文章作者"""
        author_selectors = [
            '.author', '.user-name', '[data-author]',
            '[class*="author"]', '[class*="user"]'
        ]

        for selector in author_selectors:
            author_element = element.select_one(selector)
            if author_element:
                return author_element.get_text(strip=True)

        return ""

    def _extract_post_time(self, element) -> str:
        """提取文章时间"""
        time_selectors = [
            '.time', '.publish-time', '[data-time]',
            '[class*="time"]', '[class*="date"]'
        ]

        for selector in time_selectors:
            time_element = element.select_one(selector)
            if time_element:
                return time_element.get_text(strip=True)

        return ""

    def _extract_post_likes(self, element) -> int:
        """提取文章点赞数"""
        likes_selectors = [
            '.likes', '.fav-count', '[data-likes]',
            '[class*="like"]', '[class*="fav"]'
        ]

        for selector in likes_selectors:
            likes_element = element.select_one(selector)
            if likes_element:
                likes_text = likes_element.get_text(strip=True)
                try:
                    return int(''.join(filter(str.isdigit, likes_text)))
                except:
                    continue

        return 0

    def _extract_post_comment_count(self, element) -> int:
        """提取文章评论数"""
        comment_selectors = [
            '.comments', '.comment-count', '[data-comments]',
            '[class*="comment"]'
        ]

        for selector in comment_selectors:
            comment_element = element.select_one(selector)
            if comment_element:
                comment_text = comment_element.get_text(strip=True)
                try:
                    return int(''.join(filter(str.isdigit, comment_text)))
                except:
                    continue

        return 0

    def _extract_post_shares(self, element) -> int:
        """提取文章分享数"""
        share_selectors = [
            '.shares', '.repost-count', '[data-shares]',
            '[class*="share"]', '[class*="repost"]'
        ]

        for selector in share_selectors:
            share_element = element.select_one(selector)
            if share_element:
                share_text = share_element.get_text(strip=True)
                try:
                    return int(''.join(filter(str.isdigit, share_text)))
                except:
                    continue

        return 0

    def _extract_post_tags(self, element) -> List[str]:
        """提取文章标签"""
        tag_selectors = [
            '.tags', '.hashtags', '[data-tags]',
            '[class*="tag"]', '[class*="hashtag"]'
        ]

        tags = []
        for selector in tag_selectors:
            tag_elements = element.select(selector)
            for tag_element in tag_elements:
                tag_text = tag_element.get_text(strip=True)
                if tag_text:
                    tags.append(tag_text)

        return tags

    def _extract_post_url(self, element) -> str:
        """提取文章URL"""
        url_selectors = ['a[href]', '[data-url]']

        for selector in url_selectors:
            url_element = element.select_one(selector)
            if url_element:
                return url_element.get('href') or url_element.get('data-url', '')

        return ""

    def _determine_post_type(self, element) -> str:
        """确定文章类型"""
        class_names = ' '.join(element.get('class', []))

        if 'repost' in class_names.lower():
            return 'repost'
        elif 'article' in class_names.lower():
            return 'article'
        elif 'status' in class_names.lower():
            return 'status'
        else:
            return 'post'

    def _analyze_post_content(self, content: str) -> Dict[str, Any]:
        """分析文章内容"""
        if not content:
            return {"type": "unknown", "length": 0}

        # 简单的内容分析
        word_count = len(content.split())
        char_count = len(content)

        # 判断内容类型
        if word_count > 500:
            content_type = "long_article"
        elif word_count > 100:
            content_type = "medium_article"
        else:
            content_type = "short_post"

        return {
            "type": content_type,
            "word_count": word_count,
            "char_count": char_count,
            "length": char_count
        }

    def _analyze_investment_sentiment(self, content: str) -> str:
        """分析投资情感"""
        if not content:
            return "neutral"

        # 投资相关的正面词汇
        bullish_words = ["看涨", "买入", "持有", "上涨", "利好", "牛市", "突破", "强势"]
        # 投资相关的负面词汇
        bearish_words = ["看跌", "卖出", "下跌", "利空", "熊市", "破位", "弱势", "风险"]

        bullish_count = sum(1 for word in bullish_words if word in content)
        bearish_count = sum(1 for word in bearish_words if word in content)

        if bullish_count > bearish_count:
            return "bullish"
        elif bearish_count > bullish_count:
            return "bearish"
        else:
            return "neutral"

    def _summarize_creator_content(self, posts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """汇总创作者内容"""
        if not posts_data:
            return {"total_posts": 0, "main_topics": [], "avg_engagement": 0}

        total_likes = sum(post.get("likes", 0) for post in posts_data)
        total_comments = sum(post.get("comments", 0) for post in posts_data)
        total_posts = len(posts_data)

        # 提取主要话题（简化实现）
        all_tags = []
        for post in posts_data:
            all_tags.extend(post.get("tags", []))

        # 统计最常见的标签
        from collections import Counter
        tag_counts = Counter(all_tags)
        main_topics = [tag for tag, count in tag_counts.most_common(5)]

        return {
            "total_posts": total_posts,
            "avg_likes": total_likes / total_posts if total_posts > 0 else 0,
            "avg_comments": total_comments / total_posts if total_posts > 0 else 0,
            "main_topics": main_topics,
            "engagement_score": (total_likes + total_comments) / total_posts if total_posts > 0 else 0
        }

    def _calculate_influence_score(self, profile_result: Dict[str, Any],
                                 posts_result: Dict[str, Any],
                                 engagement_result: Dict[str, Any]) -> float:
        """计算影响力分数"""
        try:
            # 基础分数
            base_score = 0.0

            # 从文章数据计算
            if posts_result.get("success"):
                content_summary = posts_result.get("content_summary", {})
                avg_likes = content_summary.get("avg_likes", 0)
                avg_comments = content_summary.get("avg_comments", 0)
                total_posts = content_summary.get("total_posts", 0)

                # 影响力计算公式（简化）
                base_score = (avg_likes * 0.3 + avg_comments * 0.5 + total_posts * 0.2) / 10

            # 限制分数范围
            return min(max(base_score, 0.0), 100.0)

        except Exception as e:
            logger.warning(f"计算影响力分数失败: {e}")
            return 0.0
