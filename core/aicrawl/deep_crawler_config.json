{"description": "雪球深度爬取系统配置 - 个股和创作者优先", "version": "1.0.0", "basic_crawl": {"homepage_interval": 20, "stock_comments_interval": 45, "trending_stocks_interval": 10, "max_comments_per_stock": 80, "max_trending_stocks": 150, "max_homepage_posts": 300}, "deep_crawl": {"enable_deep_crawl": true, "deep_stock_interval": 90, "deep_creator_interval": 120, "deep_crawl_priority_stocks": 30, "deep_crawl_priority_creators": 80}, "performance": {"max_concurrent_tasks": 4, "max_stocks_per_batch": 8, "enable_anti_detection": true, "random_delay_range": [3, 12]}, "data_management": {"data_retention_days": 45, "max_retries": 4, "retry_delay": 240}, "priority_stocks": ["SH000001", "SZ399001", "SH000300", "SH600036", "SH600519", "SZ000858", "SZ300750", "SH688981", "SZ002415", "SH600276", "SZ000002", "SH601318", "SH600000", "SZ002594", "SH688599"], "priority_creators": ["example_creator_1", "example_creator_2", "example_creator_3"], "crawl_strategies": {"stock_deep_crawl": {"max_comments": 300, "max_scroll_attempts": 20, "include_analysis": true, "include_news": true, "include_historical": true, "scroll_delay": 2000, "wait_for_content": 5000}, "creator_deep_crawl": {"max_posts": 150, "max_scroll_attempts": 15, "include_history": true, "include_followers": true, "include_full_profile": true, "scroll_delay": 1800, "wait_for_content": 4000}}, "content_extraction": {"enable_sentiment_analysis": true, "enable_stock_mention_extraction": true, "enable_content_classification": true, "enable_influence_scoring": true}, "monitoring": {"enable_detailed_logging": true, "log_level": "INFO", "performance_tracking": true, "error_alerting": true}, "advanced_features": {"enable_multi_page_crawl": true, "enable_related_content": true, "enable_historical_data": true, "enable_real_time_updates": false, "enable_predictive_crawling": false}}