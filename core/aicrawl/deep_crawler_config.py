"""
深度爬取配置 - 个股和创作者优先的深度优化
"""
from dataclasses import dataclass
from typing import Dict, List, Any, Optional
from enum import Enum

class CrawlDepth(Enum):
    """爬取深度级别"""
    SHALLOW = "shallow"      # 浅层：基础信息
    MEDIUM = "medium"        # 中层：详细信息
    DEEP = "deep"           # 深层：全面信息
    ULTRA_DEEP = "ultra_deep"  # 超深：历史数据

class ContentPriority(Enum):
    """内容优先级"""
    CRITICAL = 1    # 关键内容：个股核心数据、顶级创作者
    HIGH = 2        # 高优先级：活跃股票、知名创作者
    MEDIUM = 3      # 中优先级：一般股票、普通创作者
    LOW = 4         # 低优先级：冷门内容

@dataclass
class DeepCrawlConfig:
    """深度爬取配置"""
    # 个股深度配置
    stock_crawl_depth: CrawlDepth = CrawlDepth.DEEP
    max_comments_per_stock: int = 200  # 大幅增加评论数量
    max_historical_days: int = 30      # 历史数据天数
    include_stock_analysis: bool = True # 包含分析文章
    include_news: bool = True          # 包含相关新闻
    include_announcements: bool = True  # 包含公告
    
    # 创作者深度配置
    creator_crawl_depth: CrawlDepth = CrawlDepth.DEEP
    max_posts_per_creator: int = 100   # 每个创作者最多爬取文章数
    max_creators_to_track: int = 200   # 跟踪的创作者数量
    include_creator_history: bool = True # 包含历史文章
    include_follower_data: bool = True   # 包含粉丝数据
    
    # 滚动和加载配置
    max_scroll_attempts: int = 15      # 增加滚动次数
    scroll_delay: int = 2000          # 滚动延迟
    load_more_attempts: int = 10      # 加载更多尝试次数
    wait_for_content: int = 5000      # 等待内容加载时间
    
    # 优先级配置
    priority_stocks: List[str] = None  # 优先股票列表
    priority_creators: List[str] = None # 优先创作者列表
    
    # 深度爬取策略
    enable_multi_page_crawl: bool = True    # 启用多页爬取
    enable_related_content: bool = True     # 启用相关内容爬取
    enable_historical_data: bool = True     # 启用历史数据爬取
    
    def __post_init__(self):
        if self.priority_stocks is None:
            self.priority_stocks = []
        if self.priority_creators is None:
            self.priority_creators = []

class DeepScrollStrategy:
    """深度滚动策略"""
    
    # 超深度滚动配置
    ULTRA_DEEP_SCROLL = {
        "max_scrolls": 20,
        "scroll_delay": 2500,
        "step_delay": 1500,
        "positions": [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0],
        "retry_on_no_new_content": 3,
        "wait_for_lazy_load": 3000,
        "click_expand_all": True,
        "handle_infinite_scroll": True
    }
    
    # 个股专用滚动策略
    STOCK_DEEP_SCROLL = {
        "max_scrolls": 15,
        "scroll_delay": 2000,
        "step_delay": 1200,
        "positions": [0.2, 0.4, 0.6, 0.8, 1.0],
        "focus_on_comments": True,
        "expand_all_replies": True,
        "load_historical_comments": True,
        "extract_comment_threads": True
    }
    
    # 创作者专用滚动策略
    CREATOR_DEEP_SCROLL = {
        "max_scrolls": 12,
        "scroll_delay": 1800,
        "step_delay": 1000,
        "positions": [0.25, 0.5, 0.75, 1.0],
        "focus_on_articles": True,
        "extract_full_content": True,
        "include_article_stats": True,
        "follow_article_links": True
    }
    
    @classmethod
    def get_strategy_for_content_type(cls, content_type: str, depth: CrawlDepth) -> Dict[str, Any]:
        """根据内容类型和深度获取滚动策略"""
        if content_type == "stock":
            base_strategy = cls.STOCK_DEEP_SCROLL.copy()
        elif content_type == "creator":
            base_strategy = cls.CREATOR_DEEP_SCROLL.copy()
        else:
            base_strategy = cls.ULTRA_DEEP_SCROLL.copy()
        
        # 根据深度调整策略
        if depth == CrawlDepth.ULTRA_DEEP:
            base_strategy["max_scrolls"] *= 2
            base_strategy["scroll_delay"] += 500
        elif depth == CrawlDepth.SHALLOW:
            base_strategy["max_scrolls"] //= 2
            base_strategy["scroll_delay"] -= 500
        
        return base_strategy

class ContentExtractor:
    """深度内容提取器"""
    
    # 个股深度提取规则
    STOCK_DEEP_SELECTORS = {
        "basic_info": {
            "symbol": [".stock-symbol", "[data-symbol]", ".symbol"],
            "name": [".stock-name", "[data-name]", ".name"],
            "price": [".current-price", ".price", "[data-price]"],
            "change": [".change", ".percent", "[data-change]"],
            "volume": [".volume", "[data-volume]"],
            "market_cap": [".market-cap", "[data-market-cap]"]
        },
        "detailed_info": {
            "pe_ratio": [".pe", ".pe-ratio", "[data-pe]"],
            "pb_ratio": [".pb", ".pb-ratio", "[data-pb]"],
            "dividend_yield": [".dividend", "[data-dividend]"],
            "52_week_high": [".high-52w", "[data-high]"],
            "52_week_low": [".low-52w", "[data-low]"],
            "beta": [".beta", "[data-beta]"],
            "eps": [".eps", "[data-eps]"]
        },
        "comments": {
            "comment_text": [".comment-content", ".text", ".content"],
            "comment_author": [".author", ".user-name", "[data-author]"],
            "comment_time": [".time", ".timestamp", "[data-time]"],
            "comment_likes": [".likes", ".fav-count", "[data-likes]"],
            "comment_replies": [".replies", ".reply-count", "[data-replies]"],
            "comment_sentiment": "[data-sentiment]"
        },
        "analysis": {
            "analyst_rating": [".rating", ".recommendation"],
            "target_price": [".target-price", "[data-target]"],
            "analysis_text": [".analysis", ".research-note"],
            "analysis_author": [".analyst", ".author"]
        }
    }
    
    # 创作者深度提取规则
    CREATOR_DEEP_SELECTORS = {
        "profile": {
            "username": [".username", ".screen-name", "[data-username]"],
            "display_name": [".display-name", ".name"],
            "bio": [".bio", ".description", ".intro"],
            "follower_count": [".followers", "[data-followers]"],
            "following_count": [".following", "[data-following]"],
            "post_count": [".posts", "[data-posts]"],
            "verified": [".verified", "[data-verified]"],
            "join_date": [".join-date", "[data-join]"]
        },
        "posts": {
            "post_title": [".title", ".post-title", "h1", "h2"],
            "post_content": [".content", ".post-content", ".article-content"],
            "post_time": [".time", ".publish-time", "[data-time]"],
            "post_likes": [".likes", ".fav-count", "[data-likes]"],
            "post_comments": [".comments", ".comment-count", "[data-comments]"],
            "post_shares": [".shares", ".repost-count", "[data-shares]"],
            "post_tags": [".tags", ".hashtags", "[data-tags]"],
            "mentioned_stocks": [".stock-mention", "[data-stock]"]
        },
        "engagement": {
            "avg_likes": "[data-avg-likes]",
            "engagement_rate": "[data-engagement]",
            "influence_score": "[data-influence]",
            "expertise_areas": [".expertise", "[data-expertise]"]
        }
    }
    
    @classmethod
    def get_selectors_for_content(cls, content_type: str, detail_level: str = "all") -> Dict[str, Any]:
        """获取内容提取选择器"""
        if content_type == "stock":
            selectors = cls.STOCK_DEEP_SELECTORS
        elif content_type == "creator":
            selectors = cls.CREATOR_DEEP_SELECTORS
        else:
            return {}
        
        if detail_level == "all":
            return selectors
        else:
            return selectors.get(detail_level, {})

class PriorityManager:
    """优先级管理器"""
    
    def __init__(self, config: DeepCrawlConfig):
        self.config = config
        self.stock_priorities = {}
        self.creator_priorities = {}
        
    def set_stock_priority(self, symbol: str, priority: ContentPriority):
        """设置股票优先级"""
        self.stock_priorities[symbol] = priority
        
    def set_creator_priority(self, creator_id: str, priority: ContentPriority):
        """设置创作者优先级"""
        self.creator_priorities[creator_id] = priority
        
    def get_stock_crawl_config(self, symbol: str) -> Dict[str, Any]:
        """获取股票爬取配置"""
        priority = self.stock_priorities.get(symbol, ContentPriority.MEDIUM)
        
        base_config = {
            "max_comments": self.config.max_comments_per_stock,
            "scroll_strategy": "stock_deep",
            "include_analysis": self.config.include_stock_analysis,
            "include_news": self.config.include_news
        }
        
        # 根据优先级调整配置
        if priority == ContentPriority.CRITICAL:
            base_config["max_comments"] *= 2
            base_config["crawl_depth"] = CrawlDepth.ULTRA_DEEP
            base_config["include_historical"] = True
        elif priority == ContentPriority.HIGH:
            base_config["max_comments"] = int(base_config["max_comments"] * 1.5)
            base_config["crawl_depth"] = CrawlDepth.DEEP
        elif priority == ContentPriority.LOW:
            base_config["max_comments"] //= 2
            base_config["crawl_depth"] = CrawlDepth.SHALLOW
            
        return base_config
        
    def get_creator_crawl_config(self, creator_id: str) -> Dict[str, Any]:
        """获取创作者爬取配置"""
        priority = self.creator_priorities.get(creator_id, ContentPriority.MEDIUM)
        
        base_config = {
            "max_posts": self.config.max_posts_per_creator,
            "scroll_strategy": "creator_deep",
            "include_history": self.config.include_creator_history,
            "include_followers": self.config.include_follower_data
        }
        
        # 根据优先级调整配置
        if priority == ContentPriority.CRITICAL:
            base_config["max_posts"] *= 2
            base_config["crawl_depth"] = CrawlDepth.ULTRA_DEEP
            base_config["include_full_profile"] = True
        elif priority == ContentPriority.HIGH:
            base_config["max_posts"] = int(base_config["max_posts"] * 1.5)
            base_config["crawl_depth"] = CrawlDepth.DEEP
        elif priority == ContentPriority.LOW:
            base_config["max_posts"] //= 2
            base_config["crawl_depth"] = CrawlDepth.SHALLOW
            
        return base_config
        
    def get_priority_sorted_stocks(self, stock_list: List[str]) -> List[str]:
        """按优先级排序股票列表"""
        def get_priority_value(symbol):
            priority = self.stock_priorities.get(symbol, ContentPriority.MEDIUM)
            return priority.value
            
        return sorted(stock_list, key=get_priority_value)
        
    def get_priority_sorted_creators(self, creator_list: List[str]) -> List[str]:
        """按优先级排序创作者列表"""
        def get_priority_value(creator_id):
            priority = self.creator_priorities.get(creator_id, ContentPriority.MEDIUM)
            return priority.value
            
        return sorted(creator_list, key=get_priority_value)
