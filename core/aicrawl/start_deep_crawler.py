#!/usr/bin/env python3
"""
雪球深度爬取系统启动脚本
专注于个股和创作者的深度数据挖掘
"""
import asyncio
import argparse
import logging
import json
import sys
from pathlib import Path
from datetime import datetime

from auto_crawler import AutoCrawlerScheduler, AutoCrawlerConfig
from deep_crawler_config import DeepCrawlConfig, CrawlDepth, ContentPriority
from api_server import run_api_server

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deep_crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DeepCrawlerLauncher:
    """深度爬取系统启动器"""
    
    def __init__(self):
        self.scheduler = None
        
    def load_deep_config(self, config_file: str = "deep_crawler_config.json") -> AutoCrawlerConfig:
        """加载深度爬取配置"""
        default_config = {
            # 基础爬取配置
            "homepage_interval": 20,
            "stock_comments_interval": 45,
            "trending_stocks_interval": 10,
            "max_comments_per_stock": 80,
            "max_trending_stocks": 150,
            "max_homepage_posts": 300,
            
            # 深度爬取配置
            "enable_deep_crawl": True,
            "deep_stock_interval": 90,
            "deep_creator_interval": 120,
            "deep_crawl_priority_stocks": 30,
            "deep_crawl_priority_creators": 80,
            
            # 性能配置
            "max_concurrent_tasks": 4,
            "max_stocks_per_batch": 8,
            "enable_anti_detection": True,
            "random_delay_range": [3, 12],
            
            # 数据管理
            "data_retention_days": 45,
            "max_retries": 4,
            "retry_delay": 240
        }
        
        if Path(config_file).exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    
                    # 合并基础配置
                    if 'basic_crawl' in user_config:
                        default_config.update(user_config['basic_crawl'])
                    
                    # 合并深度爬取配置
                    if 'deep_crawl' in user_config:
                        default_config.update(user_config['deep_crawl'])
                    
                    # 合并性能配置
                    if 'performance' in user_config:
                        default_config.update(user_config['performance'])
                    
                    # 合并数据管理配置
                    if 'data_management' in user_config:
                        default_config.update(user_config['data_management'])
                
                logger.info(f"已加载深度爬取配置: {config_file}")
            except Exception as e:
                logger.warning(f"加载配置文件失败，使用默认配置: {e}")
        else:
            logger.info("使用默认深度爬取配置")
        
        return AutoCrawlerConfig(**default_config)
    
    def create_optimized_config(self, mode: str = "balanced") -> AutoCrawlerConfig:
        """创建优化的配置"""
        if mode == "aggressive":
            # 激进模式：最大深度，最高频率
            return AutoCrawlerConfig(
                homepage_interval=15,
                stock_comments_interval=30,
                trending_stocks_interval=8,
                deep_stock_interval=60,
                deep_creator_interval=90,
                max_comments_per_stock=150,
                max_trending_stocks=200,
                deep_crawl_priority_stocks=50,
                deep_crawl_priority_creators=100,
                max_concurrent_tasks=6,
                enable_deep_crawl=True,
                enable_anti_detection=True
            )
        elif mode == "conservative":
            # 保守模式：稳定运行，较低频率
            return AutoCrawlerConfig(
                homepage_interval=45,
                stock_comments_interval=90,
                trending_stocks_interval=20,
                deep_stock_interval=180,
                deep_creator_interval=240,
                max_comments_per_stock=50,
                max_trending_stocks=80,
                deep_crawl_priority_stocks=15,
                deep_crawl_priority_creators=40,
                max_concurrent_tasks=2,
                enable_deep_crawl=True,
                enable_anti_detection=True
            )
        else:  # balanced
            # 平衡模式：性能与稳定性兼顾
            return AutoCrawlerConfig(
                homepage_interval=25,
                stock_comments_interval=60,
                trending_stocks_interval=12,
                deep_stock_interval=120,
                deep_creator_interval=150,
                max_comments_per_stock=100,
                max_trending_stocks=120,
                deep_crawl_priority_stocks=25,
                deep_crawl_priority_creators=60,
                max_concurrent_tasks=4,
                enable_deep_crawl=True,
                enable_anti_detection=True
            )
    
    async def run_deep_crawler(self, config: AutoCrawlerConfig):
        """运行深度爬取系统"""
        logger.info("🚀 启动雪球深度爬取系统...")
        logger.info("🎯 专注于个股和创作者的深度数据挖掘")
        
        self.scheduler = AutoCrawlerScheduler(config)
        
        try:
            await self.scheduler.start()
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止深度爬取系统...")
        except Exception as e:
            logger.error(f"深度爬取系统运行异常: {e}")
            raise
        finally:
            if self.scheduler:
                await self.scheduler.stop()
    
    def show_deep_crawler_status(self):
        """显示深度爬取系统状态"""
        print("=" * 60)
        print("🔍 雪球深度爬取系统状态")
        print("=" * 60)
        
        # 检查配置文件
        config_file = Path("deep_crawler_config.json")
        if config_file.exists():
            print(f"✓ 深度配置文件: {config_file}")
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    print(f"  - 深度爬取: {'启用' if config_data.get('deep_crawl', {}).get('enable_deep_crawl', False) else '禁用'}")
                    print(f"  - 优先股票数: {config_data.get('deep_crawl', {}).get('deep_crawl_priority_stocks', 0)}")
                    print(f"  - 优先创作者数: {config_data.get('deep_crawl', {}).get('deep_crawl_priority_creators', 0)}")
            except:
                print("  ⚠️  配置文件格式错误")
        else:
            print("✗ 深度配置文件不存在")
        
        # 检查数据库
        db_file = Path("xueqiu_crawl_data.db")
        if db_file.exists():
            size_mb = db_file.stat().st_size / (1024 * 1024)
            print(f"✓ 数据库文件: {db_file} ({size_mb:.2f} MB)")
        else:
            print("✗ 数据库文件不存在")
        
        # 检查日志文件
        log_files = ["deep_crawler.log", "auto_crawler.log"]
        for log_file in log_files:
            log_path = Path(log_file)
            if log_path.exists():
                size_kb = log_path.stat().st_size / 1024
                print(f"✓ 日志文件: {log_file} ({size_kb:.1f} KB)")
        
        print("\n💡 使用说明:")
        print("  python start_deep_crawler.py --mode balanced  # 平衡模式")
        print("  python start_deep_crawler.py --mode aggressive  # 激进模式")
        print("  python start_deep_crawler.py --mode conservative  # 保守模式")
        print("  python start_deep_crawler.py --config deep_crawler_config.json  # 自定义配置")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='雪球深度爬取系统')
    
    # 运行模式
    parser.add_argument('--mode', choices=['balanced', 'aggressive', 'conservative'], 
                       default='balanced', help='运行模式')
    
    # 配置文件
    parser.add_argument('--config', type=str, default='deep_crawler_config.json',
                       help='配置文件路径')
    
    # API服务器
    parser.add_argument('--api', action='store_true', help='启动API服务器模式')
    parser.add_argument('--port', type=int, default=8000, help='API服务器端口')
    
    # 工具选项
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    parser.add_argument('--create-config', action='store_true', help='创建示例配置文件')
    
    # 日志级别
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    launcher = DeepCrawlerLauncher()
    
    try:
        if args.status:
            launcher.show_deep_crawler_status()
            return
        
        if args.create_config:
            # 创建示例配置文件的逻辑
            print("示例配置文件已存在: deep_crawler_config.json")
            return
        
        # 加载配置
        if Path(args.config).exists():
            config = launcher.load_deep_config(args.config)
            print(f"📋 使用配置文件: {args.config}")
        else:
            config = launcher.create_optimized_config(args.mode)
            print(f"📋 使用 {args.mode} 模式配置")
        
        # 显示配置信息
        print(f"🔧 配置概览:")
        print(f"  - 深度爬取: {'启用' if config.enable_deep_crawl else '禁用'}")
        print(f"  - 首页间隔: {config.homepage_interval} 分钟")
        print(f"  - 深度股票间隔: {config.deep_stock_interval} 分钟")
        print(f"  - 深度创作者间隔: {config.deep_creator_interval} 分钟")
        print(f"  - 最大并发: {config.max_concurrent_tasks}")
        print(f"  - 优先股票数: {config.deep_crawl_priority_stocks}")
        print(f"  - 优先创作者数: {config.deep_crawl_priority_creators}")
        
        if args.api:
            print(f"🌐 启动API服务器模式: http://localhost:{args.port}")
            run_api_server("0.0.0.0", args.port)
        else:
            print("🚀 启动深度爬取模式...")
            print("💡 按 Ctrl+C 停止系统")
            asyncio.run(launcher.run_deep_crawler(config))
        
    except KeyboardInterrupt:
        print("\n⌨️  收到中断信号，系统停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
