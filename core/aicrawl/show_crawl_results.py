#!/usr/bin/env python3
"""
展示雪球今日页面爬取结果的脚本
"""

import json
import sys
from pathlib import Path
from datetime import datetime

def show_crawl_results(json_file: str):
    """展示爬取结果"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print("="*80)
        print("雪球今日页面爬取结果详细展示")
        print("="*80)
        
        # 基本信息
        print(f"📊 爬取状态: {'✅ 成功' if data['success'] else '❌ 失败'}")
        print(f"🌐 页面URL: {data['url']}")
        print(f"⏰ 爬取时间: {data['crawl_time']}")
        print(f"⏱️  响应时间: {data['response_time']:.2f}秒")
        
        if 'crawl_type' in data:
            print(f"🔧 爬取类型: {data['crawl_type']}")
            print(f"📜 最大滚动次数: {data.get('max_scrolls', 'N/A')}")
            print(f"⏳ 滚动耐心值: {data.get('scroll_patience', 'N/A')}")
        
        if not data['success']:
            print(f"❌ 错误信息: {data.get('error', 'Unknown error')}")
            return
        
        crawl_data = data['data']
        
        # 数据统计
        if 'summary' in crawl_data:
            summary = crawl_data['summary']
            print("\n📈 数据统计:")
            print(f"  📝 动态/帖子: {summary.get('total_posts', 0)} 条")
            print(f"  📈 股票信息: {summary.get('total_stocks', 0)} 只")
            print(f"  🏷️  话题标签: {summary.get('total_topics', 0)} 个")
            print(f"  👤 用户信息: {summary.get('total_users', 0)} 个")
            print(f"  📦 总内容项: {summary.get('total_items', 0)} 项")
        else:
            # 兼容旧格式
            posts = crawl_data.get('posts', [])
            stocks = crawl_data.get('stocks', [])
            topics = crawl_data.get('hot_topics', [])
            print(f"  📝 动态/帖子: {len(posts)} 条")
            print(f"  📈 股票信息: {len(stocks)} 只")
            print(f"  🏷️  话题标签: {len(topics)} 个")
        
        # 展示热门股票
        print("\n📈 热门股票信息:")
        print("-" * 60)
        hot_stocks = crawl_data.get('hot_stocks', crawl_data.get('stocks', []))
        if hot_stocks:
            for i, stock in enumerate(hot_stocks[:10], 1):  # 只显示前10个
                symbol = stock.get('symbol', 'N/A')
                name = stock.get('name', 'N/A')
                if len(name) > 50:
                    name = name[:50] + "..."
                print(f"  {i:2d}. {symbol} - {name}")
        else:
            print("  暂无股票信息")
        
        # 展示话题标签
        print("\n🏷️ 话题标签:")
        print("-" * 60)
        topics = crawl_data.get('topics', crawl_data.get('hot_topics', []))
        if topics:
            for i, topic in enumerate(topics[:10], 1):  # 只显示前10个
                title = topic.get('title', 'N/A')
                if len(title) > 60:
                    title = title[:60] + "..."
                print(f"  {i:2d}. {title}")
        else:
            print("  暂无话题信息")
        
        # 展示用户信息
        print("\n👤 活跃用户:")
        print("-" * 60)
        users = crawl_data.get('users', [])
        if users:
            for i, user in enumerate(users[:10], 1):  # 只显示前10个
                username = user.get('username', 'N/A')
                print(f"  {i:2d}. {username}")
        else:
            print("  暂无用户信息")
        
        # 展示部分动态内容
        print("\n📝 热门动态预览:")
        print("-" * 60)
        posts = crawl_data.get('posts', [])
        if posts:
            for i, post in enumerate(posts[:3], 1):  # 只显示前3个
                content = post.get('content', 'N/A')
                author = post.get('author', '未知用户')
                timestamp = post.get('timestamp', '未知时间')
                
                # 截取内容前200字符
                if len(content) > 200:
                    content = content[:200] + "..."
                
                print(f"  {i}. 【{author}】{timestamp}")
                print(f"     {content}")
                print()
        else:
            print("  暂无动态内容")
        
        print("="*80)
        print(f"📄 完整数据已保存在: {json_file}")
        print("="*80)
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {json_file}")
    except json.JSONDecodeError:
        print(f"❌ JSON文件格式错误: {json_file}")
    except Exception as e:
        print(f"❌ 读取文件时发生错误: {e}")

def list_result_files():
    """列出所有结果文件"""
    current_dir = Path('.')
    result_files = []
    
    # 查找所有结果文件
    for pattern in ['xueqiu_today_result_*.json', 'xueqiu_today_enhanced_*.json']:
        result_files.extend(current_dir.glob(pattern))
    
    if not result_files:
        print("❌ 未找到任何爬取结果文件")
        return []
    
    # 按修改时间排序
    result_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    
    print("📁 找到以下爬取结果文件:")
    print("-" * 60)
    for i, file in enumerate(result_files, 1):
        mtime = datetime.fromtimestamp(file.stat().st_mtime)
        file_size = file.stat().st_size / 1024  # KB
        print(f"  {i:2d}. {file.name}")
        print(f"      修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"      文件大小: {file_size:.1f} KB")
        print()
    
    return result_files

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='展示雪球今日页面爬取结果')
    parser.add_argument('--file', '-f', help='指定要展示的JSON结果文件')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有结果文件')
    parser.add_argument('--latest', action='store_true', help='展示最新的结果文件')
    
    args = parser.parse_args()
    
    if args.list:
        list_result_files()
        return
    
    if args.latest:
        result_files = list_result_files()
        if result_files:
            print(f"\n🔍 展示最新结果文件: {result_files[0].name}")
            print("="*80)
            show_crawl_results(str(result_files[0]))
        return
    
    if args.file:
        show_crawl_results(args.file)
    else:
        # 如果没有指定文件，列出所有文件并让用户选择
        result_files = list_result_files()
        if not result_files:
            return
        
        try:
            choice = input(f"\n请选择要展示的文件 (1-{len(result_files)}) 或按回车查看最新文件: ").strip()
            
            if not choice:
                # 默认选择最新文件
                selected_file = result_files[0]
            else:
                index = int(choice) - 1
                if 0 <= index < len(result_files):
                    selected_file = result_files[index]
                else:
                    print("❌ 无效的选择")
                    return
            
            print(f"\n🔍 展示文件: {selected_file.name}")
            print("="*80)
            show_crawl_results(str(selected_file))
            
        except (ValueError, KeyboardInterrupt):
            print("\n👋 已取消")

if __name__ == "__main__":
    main()
