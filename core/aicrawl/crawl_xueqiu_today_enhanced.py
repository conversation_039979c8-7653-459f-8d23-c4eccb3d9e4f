#!/usr/bin/env python3
"""
雪球今日页面增强版滚动深度爬取脚本
支持超深度滚动、智能内容识别和数据去重
"""

import asyncio
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Set
from pathlib import Path
import hashlib

from crawler_engine import SmartCrawler, CrawlResult, XueqiuCrawler
from config import AntiDetectionConfig, ScrollConfig
from extractors import XueqiuHomePageData

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xueqiu_today_enhanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class XueqiuTodayEnhancedCrawler:
    """雪球今日页面增强版爬虫 - 支持超深度滚动"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = XueqiuCrawler(enable_anti_detection=enable_anti_detection)
        self.today_url = AntiDetectionConfig.XUEQIU_CONFIG["today_url"]
        self.seen_content: Set[str] = set()  # 用于去重
        
    async def ultra_deep_crawl(self, max_scrolls: int = 50, scroll_patience: int = 5) -> Dict[str, Any]:
        """
        超深度爬取雪球今日页面
        
        Args:
            max_scrolls: 最大滚动次数
            scroll_patience: 滚动耐心值（连续无新内容的滚动次数）
        """
        logger.info(f"开始超深度爬取雪球今日页面: {self.today_url}")
        logger.info(f"最大滚动次数: {max_scrolls}, 滚动耐心值: {scroll_patience}")
        
        start_time = time.time()
        
        # 生成超深度滚动的JavaScript代码
        ultra_deep_js = self._generate_ultra_deep_scroll_js(max_scrolls, scroll_patience)
        
        try:
            # 使用超长时间配置进行深度爬取
            result = await self.crawler._crawl_with_crawl4ai(
                self.today_url,
                js_code=ultra_deep_js,
                page_timeout=180000,  # 3分钟超时
                delay_before_return_html=20,  # 等待20秒确保所有内容加载完成
            )
            
            if result.success:
                # 提取和处理页面数据
                extracted_data = self._extract_and_process_data(result.data)
                
                # 构建结果
                crawl_result = {
                    "success": True,
                    "url": self.today_url,
                    "crawl_time": datetime.now().isoformat(),
                    "crawl_type": "ultra_deep",
                    "max_scrolls": max_scrolls,
                    "scroll_patience": scroll_patience,
                    "response_time": time.time() - start_time,
                    "data": extracted_data,
                    "raw_html_length": len(result.data),
                    "data_type": "today_page_enhanced"
                }
                
                # 保存结果
                await self._save_enhanced_result(crawl_result)
                
                logger.info(f"超深度爬取成功，耗时: {crawl_result['response_time']:.2f}秒")
                logger.info(f"提取到 {len(extracted_data.get('posts', []))} 条动态")
                logger.info(f"提取到 {len(extracted_data.get('hot_stocks', []))} 只热门股票")
                logger.info(f"提取到 {len(extracted_data.get('topics', []))} 个话题")
                
                return crawl_result
                
            else:
                error_result = {
                    "success": False,
                    "url": self.today_url,
                    "error": result.error,
                    "crawl_time": datetime.now().isoformat(),
                    "response_time": time.time() - start_time
                }
                logger.error(f"超深度爬取失败: {result.error}")
                return error_result
                
        except Exception as e:
            error_result = {
                "success": False,
                "url": self.today_url,
                "error": str(e),
                "crawl_time": datetime.now().isoformat(),
                "response_time": time.time() - start_time
            }
            logger.error(f"爬取过程中发生异常: {e}")
            return error_result
    
    def _generate_ultra_deep_scroll_js(self, max_scrolls: int, scroll_patience: int) -> List[str]:
        """生成超深度滚动JavaScript代码"""
        
        js_code = [
            # 等待页面初始加载
            "await new Promise(resolve => setTimeout(resolve, 5000));",
            
            # 超深度滚动逻辑
            f"""
            console.log('🚀 开始雪球今日页面超深度滚动爬取');
            
            // 内容统计和去重
            let seenContent = new Set();
            let noNewContentCount = 0;
            const maxScrolls = {max_scrolls};
            const scrollPatience = {scroll_patience};
            
            // 获取内容统计
            function getDetailedStats() {{
                // 动态/帖子
                const posts = document.querySelectorAll(
                    '.timeline-item, .status-item, [class*="timeline"], [class*="feed"], ' +
                    '[class*="post"], [class*="status"], .feed-item, .post-item'
                );
                
                // 股票信息
                const stocks = document.querySelectorAll(
                    '[class*="stock"], [class*="symbol"], [class*="quote"], ' +
                    'a[href*="/S/"], .stock-item, .symbol-item'
                );
                
                // 话题标签
                const topics = document.querySelectorAll(
                    '[class*="topic"], [class*="tag"], [class*="hashtag"], ' +
                    'a[href*="/k?q="], .topic-item, .tag-item'
                );
                
                // 用户信息
                const users = document.querySelectorAll(
                    '[class*="user"], [class*="author"], .user-item, .author-item'
                );
                
                return {{
                    posts: posts.length,
                    stocks: stocks.length,
                    topics: topics.length,
                    users: users.length,
                    total: posts.length + stocks.length + topics.length + users.length
                }};
            }}
            
            // 智能展开内容
            async function smartExpandContent() {{
                let expandCount = 0;
                
                // 展开按钮选择器
                const expandSelectors = [
                    'button:contains("展开")', 'button:contains("更多")', 'button:contains("显示")',
                    'button:contains("查看")', '[class*="expand"]', '[class*="more"]',
                    '[class*="show"]', '.expand-btn', '.more-btn', '.show-more'
                ];
                
                for (let selector of expandSelectors) {{
                    const buttons = document.querySelectorAll(selector);
                    for (let btn of buttons) {{
                        if (btn.offsetParent !== null && !btn.disabled) {{
                            try {{
                                btn.click();
                                expandCount++;
                                await new Promise(resolve => setTimeout(resolve, 300));
                            }} catch (e) {{
                                console.log('展开按钮点击失败:', e);
                            }}
                        }}
                    }}
                }}
                
                return expandCount;
            }}
            
            // 智能加载更多
            async function smartLoadMore() {{
                const loadMoreSelectors = [
                    'button:contains("加载更多")', 'button:contains("查看更多")',
                    'button:contains("更多内容")', '[class*="load-more"]',
                    '[class*="show-more"]', '.load-more-btn', '.pagination-next'
                ];
                
                for (let selector of loadMoreSelectors) {{
                    const buttons = document.querySelectorAll(selector);
                    for (let btn of buttons) {{
                        if (btn.offsetParent !== null && !btn.disabled) {{
                            try {{
                                btn.click();
                                console.log('点击了加载更多按钮');
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                return true;
                            }} catch (e) {{
                                console.log('加载更多按钮点击失败:', e);
                            }}
                        }}
                    }}
                }}
                return false;
            }}
            
            // 智能滚动策略
            async function smartScroll(scrollIndex) {{
                // 多种滚动模式
                const scrollModes = [
                    // 渐进式滚动
                    () => {{
                        const positions = [0.2, 0.4, 0.6, 0.8, 1.0];
                        return positions.map(p => document.body.scrollHeight * p);
                    }},
                    // 固定步长滚动
                    () => {{
                        const step = window.innerHeight * 0.8;
                        const positions = [];
                        for (let i = step; i < document.body.scrollHeight; i += step) {{
                            positions.push(i);
                        }}
                        return positions;
                    }},
                    // 随机滚动（模拟人类行为）
                    () => {{
                        const positions = [];
                        for (let i = 0; i < 8; i++) {{
                            positions.push(Math.random() * document.body.scrollHeight);
                        }}
                        return positions.sort((a, b) => a - b);
                    }}
                ];
                
                const mode = scrollIndex % scrollModes.length;
                const positions = scrollModes[mode]();
                
                for (let position of positions) {{
                    window.scrollTo({{
                        top: position,
                        behavior: 'smooth'
                    }});
                    await new Promise(resolve => setTimeout(resolve, 800));
                    
                    // 在每个位置都尝试展开和加载
                    await smartExpandContent();
                    await smartLoadMore();
                }}
            }}
            
            // 主滚动循环
            console.log('📊 初始内容统计:', getDetailedStats());
            
            for (let i = 0; i < maxScrolls; i++) {{
                const beforeStats = getDetailedStats();
                console.log(`📈 滚动 ${{i + 1}}/${{maxScrolls}}: 当前内容 ${{beforeStats.total}} 项`);
                
                // 1. 智能展开内容
                const expandedCount = await smartExpandContent();
                if (expandedCount > 0) {{
                    console.log(`📖 展开了 ${{expandedCount}} 个折叠内容`);
                }}
                
                // 2. 智能滚动
                await smartScroll(i);
                
                // 3. 尝试加载更多
                const loadedMore = await smartLoadMore();
                if (loadedMore) {{
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }}
                
                // 4. 检查新内容
                const afterStats = getDetailedStats();
                const newContent = afterStats.total - beforeStats.total;
                console.log(`📊 滚动后统计: ${{afterStats.total}} 项 (新增: ${{newContent}})`);
                
                if (newContent === 0) {{
                    noNewContentCount++;
                    console.log(`⚠️ 无新内容计数: ${{noNewContentCount}}/${{scrollPatience}}`);
                    
                    if (noNewContentCount >= scrollPatience) {{
                        console.log('🛑 达到滚动耐心值，提前结束');
                        break;
                    }}
                }} else {{
                    noNewContentCount = 0; // 重置计数器
                }}
                
                // 随机延迟，模拟人类行为
                const randomDelay = 1000 + Math.random() * 2000;
                await new Promise(resolve => setTimeout(resolve, randomDelay));
            }}
            
            // 最终统计
            const finalStats = getDetailedStats();
            console.log('🏁 超深度滚动完成，最终统计:', finalStats);
            
            return {{
                crawlType: 'ultra_deep',
                maxScrolls: maxScrolls,
                actualScrolls: i + 1,
                scrollPatience: scrollPatience,
                noNewContentCount: noNewContentCount,
                finalStats: finalStats,
                success: finalStats.total > 0
            }};
            """
        ]
        
        return js_code
    
    def _extract_and_process_data(self, html_content: str) -> Dict[str, Any]:
        """提取和处理页面数据，包含去重和分类"""
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取动态/帖子（去重）
        posts = self._extract_unique_posts(soup)
        
        # 提取热门股票（去重）
        hot_stocks = self._extract_unique_stocks(soup)
        
        # 提取话题标签（去重）
        topics = self._extract_unique_topics(soup)
        
        # 提取用户信息（去重）
        users = self._extract_unique_users(soup)
        
        return {
            "posts": posts,
            "hot_stocks": hot_stocks,
            "topics": topics,
            "users": users,
            "summary": {
                "total_posts": len(posts),
                "total_stocks": len(hot_stocks),
                "total_topics": len(topics),
                "total_users": len(users),
                "total_items": len(posts) + len(hot_stocks) + len(topics) + len(users)
            }
        }
    
    def _extract_unique_posts(self, soup) -> List[Dict[str, Any]]:
        """提取唯一的动态/帖子"""
        posts = []
        post_selectors = [
            '.timeline-item', '.status-item', '[class*="timeline"]',
            '[class*="feed"]', '[class*="post"]', '[class*="status"]',
            '.feed-item', '.post-item'
        ]
        
        for selector in post_selectors:
            elements = soup.select(selector)
            for element in elements:
                post_data = self._extract_post_data(element)
                if post_data and self._is_unique_content(post_data['content']):
                    posts.append(post_data)
        
        return posts
    
    def _extract_unique_stocks(self, soup) -> List[Dict[str, Any]]:
        """提取唯一的股票信息"""
        stocks = []
        stock_selectors = [
            '[class*="stock"]', '[class*="symbol"]', '[class*="quote"]',
            'a[href*="/S/"]', '.stock-item', '.symbol-item'
        ]
        
        for selector in stock_selectors:
            elements = soup.select(selector)
            for element in elements:
                stock_data = self._extract_stock_data(element)
                if stock_data and self._is_unique_content(stock_data['symbol']):
                    stocks.append(stock_data)
        
        return stocks
    
    def _extract_unique_topics(self, soup) -> List[Dict[str, Any]]:
        """提取唯一的话题"""
        topics = []
        topic_selectors = [
            '[class*="topic"]', '[class*="tag"]', '[class*="hashtag"]',
            'a[href*="/k?q="]', '.topic-item', '.tag-item'
        ]
        
        for selector in topic_selectors:
            elements = soup.select(selector)
            for element in elements:
                topic_data = self._extract_topic_data(element)
                if topic_data and self._is_unique_content(topic_data['title']):
                    topics.append(topic_data)
        
        return topics
    
    def _extract_unique_users(self, soup) -> List[Dict[str, Any]]:
        """提取唯一的用户信息"""
        users = []
        user_selectors = [
            '[class*="user"]', '[class*="author"]', '.user-item', '.author-item'
        ]
        
        for selector in user_selectors:
            elements = soup.select(selector)
            for element in elements:
                user_data = self._extract_user_data(element)
                if user_data and self._is_unique_content(user_data['username']):
                    users.append(user_data)
        
        return users
    
    def _is_unique_content(self, content: str) -> bool:
        """检查内容是否唯一"""
        if not content:
            return False
        
        content_hash = hashlib.md5(content.encode()).hexdigest()
        if content_hash in self.seen_content:
            return False
        
        self.seen_content.add(content_hash)
        return True
    
    def _extract_post_data(self, element) -> Optional[Dict[str, Any]]:
        """提取动态数据"""
        try:
            text_content = element.get_text(strip=True)
            if not text_content or len(text_content) < 10:
                return None
            
            # 提取链接
            links = [a.get('href') for a in element.find_all('a', href=True)]
            
            # 提取时间
            time_element = element.find(['time', '[class*="time"]', '[class*="date"]'])
            timestamp = time_element.get_text(strip=True) if time_element else None
            
            # 提取作者
            author_element = element.find('[class*="user"], [class*="author"]')
            author = author_element.get_text(strip=True) if author_element else None
            
            return {
                "content": text_content[:1000],  # 限制长度
                "author": author,
                "timestamp": timestamp,
                "links": links,
                "type": "post"
            }
        except Exception as e:
            logger.debug(f"提取动态数据失败: {e}")
            return None
    
    def _extract_stock_data(self, element) -> Optional[Dict[str, Any]]:
        """提取股票数据"""
        try:
            text_content = element.get_text(strip=True)
            if not text_content:
                return None
            
            # 提取链接
            link = element.find('a')
            href = link.get('href') if link else None
            
            # 尝试提取股票代码
            symbol = None
            if href and '/S/' in href:
                symbol = href.split('/S/')[-1]
            
            return {
                "symbol": symbol or text_content,
                "name": text_content,
                "link": href,
                "type": "stock"
            }
        except Exception as e:
            logger.debug(f"提取股票数据失败: {e}")
            return None
    
    def _extract_topic_data(self, element) -> Optional[Dict[str, Any]]:
        """提取话题数据"""
        try:
            text_content = element.get_text(strip=True)
            if not text_content:
                return None
            
            link = element.find('a')
            href = link.get('href') if link else None
            
            return {
                "title": text_content,
                "link": href,
                "type": "topic"
            }
        except Exception as e:
            logger.debug(f"提取话题数据失败: {e}")
            return None
    
    def _extract_user_data(self, element) -> Optional[Dict[str, Any]]:
        """提取用户数据"""
        try:
            text_content = element.get_text(strip=True)
            if not text_content:
                return None
            
            link = element.find('a')
            href = link.get('href') if link else None
            
            return {
                "username": text_content,
                "profile_link": href,
                "type": "user"
            }
        except Exception as e:
            logger.debug(f"提取用户数据失败: {e}")
            return None
    
    async def _save_enhanced_result(self, result: Dict[str, Any]):
        """保存增强版结果"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"xueqiu_today_enhanced_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"增强版结果已保存到文件: {filename}")
            
        except Exception as e:
            logger.error(f"保存增强版结果失败: {e}")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='雪球今日页面增强版深度爬取工具')
    parser.add_argument('--max-scrolls', type=int, default=50, help='最大滚动次数')
    parser.add_argument('--scroll-patience', type=int, default=5, help='滚动耐心值')
    parser.add_argument('--disable-anti-detection', action='store_true', help='禁用反检测')
    
    args = parser.parse_args()
    
    # 创建增强版爬虫实例
    crawler = XueqiuTodayEnhancedCrawler(enable_anti_detection=not args.disable_anti_detection)
    
    # 开始超深度爬取
    result = await crawler.ultra_deep_crawl(
        max_scrolls=args.max_scrolls,
        scroll_patience=args.scroll_patience
    )
    
    # 输出结果摘要
    print("\n" + "="*80)
    print("雪球今日页面增强版爬取结果摘要")
    print("="*80)
    print(f"爬取状态: {'成功' if result['success'] else '失败'}")
    print(f"页面URL: {result['url']}")
    print(f"爬取时间: {result['crawl_time']}")
    print(f"响应时间: {result['response_time']:.2f}秒")
    print(f"爬取类型: {result.get('crawl_type', 'N/A')}")
    print(f"最大滚动次数: {result.get('max_scrolls', 'N/A')}")
    print(f"滚动耐心值: {result.get('scroll_patience', 'N/A')}")
    
    if result['success']:
        data = result['data']
        summary = data.get('summary', {})
        print(f"提取动态数量: {summary.get('total_posts', 0)}")
        print(f"提取股票数量: {summary.get('total_stocks', 0)}")
        print(f"提取话题数量: {summary.get('total_topics', 0)}")
        print(f"提取用户数量: {summary.get('total_users', 0)}")
        print(f"总内容项目: {summary.get('total_items', 0)}")
    else:
        print(f"错误信息: {result.get('error', 'Unknown error')}")
    
    print("="*80)

if __name__ == "__main__":
    asyncio.run(main())
