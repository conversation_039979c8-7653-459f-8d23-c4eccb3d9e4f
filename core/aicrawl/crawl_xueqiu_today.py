#!/usr/bin/env python3
"""
雪球今日页面滚动深度爬取脚本
专门针对 https://xueqiu.com/today 页面进行深度滚动爬取
"""

import asyncio
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from crawler_engine import SmartCrawler, CrawlResult, XueqiuCrawler
from config import AntiDetectionConfig, ScrollConfig
from extractors import XueqiuHomePageData
from data_storage import DataStorage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('xueqiu_today_crawler.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class XueqiuTodayCrawler:
    """雪球今日页面专用爬虫"""
    
    def __init__(self, enable_anti_detection: bool = True):
        self.crawler = XueqiuCrawler(enable_anti_detection=enable_anti_detection)
        self.storage = DataStorage()
        self.today_url = AntiDetectionConfig.XUEQIU_CONFIG["today_url"]
        
    async def crawl_today_page_deep(self, scroll_strategy: str = "aggressive", max_scrolls: int = 20) -> Dict[str, Any]:
        """
        深度爬取雪球今日页面
        
        Args:
            scroll_strategy: 滚动策略 (conservative, moderate, aggressive)
            max_scrolls: 最大滚动次数
        """
        logger.info(f"开始深度爬取雪球今日页面: {self.today_url}")
        logger.info(f"滚动策略: {scroll_strategy}, 最大滚动次数: {max_scrolls}")
        
        start_time = time.time()
        
        # 生成深度滚动的JavaScript代码
        deep_scroll_js = self._generate_today_page_scroll_js(scroll_strategy, max_scrolls)
        
        try:
            # 使用增强的爬取配置进行深度爬取
            result = await self.crawler._crawl_with_crawl4ai(
                self.today_url,
                js_code=deep_scroll_js,
                page_timeout=90000,  # 90秒超时，给足时间进行深度滚动
                delay_before_return_html=15,  # 等待15秒确保所有内容加载完成
                # 移除networkidle等待，使用固定延迟更稳定
            )
            
            if result.success:
                # 提取页面数据
                extracted_data = self._extract_today_page_data(result.data)
                
                # 保存结果
                crawl_result = {
                    "success": True,
                    "url": self.today_url,
                    "crawl_time": datetime.now().isoformat(),
                    "scroll_strategy": scroll_strategy,
                    "max_scrolls": max_scrolls,
                    "response_time": time.time() - start_time,
                    "data": extracted_data,
                    "raw_html_length": len(result.data),
                    "data_type": "today_page"
                }
                
                # 保存到数据库
                await self._save_crawl_result(crawl_result)
                
                logger.info(f"今日页面爬取成功，耗时: {crawl_result['response_time']:.2f}秒")
                logger.info(f"提取到 {len(extracted_data.get('posts', []))} 条动态")
                logger.info(f"提取到 {len(extracted_data.get('hot_topics', []))} 个热门话题")
                
                return crawl_result
                
            else:
                error_result = {
                    "success": False,
                    "url": self.today_url,
                    "error": result.error,
                    "crawl_time": datetime.now().isoformat(),
                    "response_time": time.time() - start_time
                }
                logger.error(f"今日页面爬取失败: {result.error}")
                return error_result
                
        except Exception as e:
            error_result = {
                "success": False,
                "url": self.today_url,
                "error": str(e),
                "crawl_time": datetime.now().isoformat(),
                "response_time": time.time() - start_time
            }
            logger.error(f"爬取过程中发生异常: {e}")
            return error_result
    
    def _generate_today_page_scroll_js(self, strategy: str, max_scrolls: int) -> List[str]:
        """生成今日页面专用的深度滚动JavaScript代码"""
        
        # 获取基础滚动配置
        scroll_config = ScrollConfig.get_scroll_strategy(strategy)
        
        # 针对今日页面优化的滚动代码
        js_code = [
            # 等待页面初始加载
            "await new Promise(resolve => setTimeout(resolve, 3000));",
            
            # 今日页面专用的深度滚动逻辑
            f"""
            console.log('🚀 开始雪球今日页面深度滚动爬取');
            
            // 统计函数
            function getContentStats() {{
                const posts = document.querySelectorAll('[class*="timeline"], [class*="feed"], [class*="post"], .status-item, .timeline-item');
                const topics = document.querySelectorAll('[class*="topic"], [class*="热门"], .hot-topic');
                const stocks = document.querySelectorAll('[class*="stock"], [class*="symbol"]');
                
                return {{
                    posts: posts.length,
                    topics: topics.length,
                    stocks: stocks.length,
                    total: posts.length + topics.length + stocks.length
                }};
            }}
            
            // 展开折叠内容
            async function expandContent() {{
                let expandCount = 0;
                
                // 展开"显示更多"按钮
                const expandButtons = document.querySelectorAll(
                    'button:contains("展开"), button:contains("更多"), button:contains("显示"), ' +
                    '[class*="expand"], [class*="more"], [class*="show"]'
                );
                
                for (let btn of expandButtons) {{
                    if (btn.offsetParent !== null) {{ // 确保按钮可见
                        try {{
                            btn.click();
                            expandCount++;
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }} catch (e) {{
                            console.log('展开按钮点击失败:', e);
                        }}
                    }}
                }}
                
                return expandCount;
            }}
            
            // 点击加载更多
            function clickLoadMore() {{
                const loadMoreSelectors = [
                    'button:contains("加载更多")',
                    'button:contains("查看更多")', 
                    '[class*="load-more"]',
                    '[class*="show-more"]',
                    '.timeline-load-more',
                    '.load-more-btn'
                ];
                
                for (let selector of loadMoreSelectors) {{
                    const buttons = document.querySelectorAll(selector);
                    for (let btn of buttons) {{
                        if (btn.offsetParent !== null && !btn.disabled) {{
                            try {{
                                btn.click();
                                console.log('点击了加载更多按钮');
                                return true;
                            }} catch (e) {{
                                console.log('加载更多按钮点击失败:', e);
                            }}
                        }}
                    }}
                }}
                return false;
            }}
            
            // 主滚动逻辑
            let scrollCount = 0;
            const maxScrolls = {max_scrolls};
            const scrollDelay = {scroll_config['scroll_delay']};
            const stepDelay = {scroll_config['step_delay']};
            
            console.log('📊 初始内容统计:', getContentStats());
            
            for (let i = 0; i < maxScrolls; i++) {{
                const beforeStats = getContentStats();
                console.log(`📈 滚动 ${{i + 1}}/${{maxScrolls}}: 当前内容 ${{beforeStats.total}} 项`);
                
                // 1. 展开当前可见的折叠内容
                const expandedCount = await expandContent();
                if (expandedCount > 0) {{
                    console.log(`📖 展开了 ${{expandedCount}} 个折叠内容`);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }}
                
                // 2. 渐进式滚动
                const positions = {scroll_config['positions']};
                for (let position of positions) {{
                    const targetY = document.body.scrollHeight * position;
                    window.scrollTo({{
                        top: targetY,
                        behavior: 'smooth'
                    }});
                    await new Promise(resolve => setTimeout(resolve, stepDelay));
                    
                    // 在每个位置都尝试展开内容和点击加载更多
                    await expandContent();
                    clickLoadMore();
                }}
                
                // 3. 滚动到底部
                window.scrollTo({{
                    top: document.body.scrollHeight,
                    behavior: 'smooth'
                }});
                await new Promise(resolve => setTimeout(resolve, scrollDelay));
                
                // 4. 尝试点击加载更多
                const loadedMore = clickLoadMore();
                if (loadedMore) {{
                    await new Promise(resolve => setTimeout(resolve, 3000)); // 等待新内容加载
                }}
                
                const afterStats = getContentStats();
                console.log(`📊 滚动后内容统计: ${{afterStats.total}} 项 (新增: ${{afterStats.total - beforeStats.total}})`);
                
                // 如果连续几次滚动都没有新内容，提前结束
                if (afterStats.total === beforeStats.total) {{
                    scrollCount++;
                    if (scrollCount >= 3) {{
                        console.log('🛑 连续3次滚动无新内容，提前结束');
                        break;
                    }}
                }} else {{
                    scrollCount = 0; // 重置计数器
                }}
            }}
            
            // 最终统计
            const finalStats = getContentStats();
            console.log('🏁 滚动完成，最终内容统计:', finalStats);
            
            return {{
                scrollStrategy: '{strategy}',
                maxScrolls: maxScrolls,
                actualScrolls: i + 1,
                finalStats: finalStats,
                success: finalStats.total > 0
            }};
            """
        ]
        
        return js_code
    
    def _extract_today_page_data(self, html_content: str) -> Dict[str, Any]:
        """提取今日页面的数据"""
        from bs4 import BeautifulSoup
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 提取动态/帖子
        posts = []
        post_selectors = [
            '[class*="timeline"]', '[class*="feed"]', '[class*="post"]',
            '.status-item', '.timeline-item', '[class*="status"]'
        ]
        
        for selector in post_selectors:
            elements = soup.select(selector)
            for element in elements:
                post_data = self._extract_post_data(element)
                if post_data and post_data not in posts:
                    posts.append(post_data)
        
        # 提取热门话题
        hot_topics = []
        topic_selectors = [
            '[class*="topic"]', '[class*="热门"]', '.hot-topic',
            '[class*="trending"]', '[class*="popular"]'
        ]
        
        for selector in topic_selectors:
            elements = soup.select(selector)
            for element in elements:
                topic_data = self._extract_topic_data(element)
                if topic_data and topic_data not in hot_topics:
                    hot_topics.append(topic_data)
        
        # 提取股票信息
        stocks = []
        stock_selectors = [
            '[class*="stock"]', '[class*="symbol"]', '[class*="quote"]'
        ]
        
        for selector in stock_selectors:
            elements = soup.select(selector)
            for element in elements:
                stock_data = self._extract_stock_data(element)
                if stock_data and stock_data not in stocks:
                    stocks.append(stock_data)
        
        return {
            "posts": posts,
            "hot_topics": hot_topics,
            "stocks": stocks,
            "total_content_items": len(posts) + len(hot_topics) + len(stocks)
        }
    
    def _extract_post_data(self, element) -> Optional[Dict[str, Any]]:
        """提取单个动态数据"""
        try:
            # 提取文本内容
            text_content = element.get_text(strip=True)
            if not text_content or len(text_content) < 10:
                return None
            
            # 提取链接
            links = [a.get('href') for a in element.find_all('a', href=True)]
            
            # 提取时间信息
            time_element = element.find(['time', '[class*="time"]', '[class*="date"]'])
            timestamp = time_element.get_text(strip=True) if time_element else None
            
            return {
                "content": text_content[:500],  # 限制长度
                "links": links,
                "timestamp": timestamp,
                "type": "post"
            }
        except Exception as e:
            logger.debug(f"提取动态数据失败: {e}")
            return None
    
    def _extract_topic_data(self, element) -> Optional[Dict[str, Any]]:
        """提取话题数据"""
        try:
            text_content = element.get_text(strip=True)
            if not text_content:
                return None
            
            # 提取链接
            link = element.find('a')
            href = link.get('href') if link else None
            
            return {
                "title": text_content,
                "link": href,
                "type": "topic"
            }
        except Exception as e:
            logger.debug(f"提取话题数据失败: {e}")
            return None
    
    def _extract_stock_data(self, element) -> Optional[Dict[str, Any]]:
        """提取股票数据"""
        try:
            text_content = element.get_text(strip=True)
            if not text_content:
                return None
            
            # 尝试提取股票代码和名称
            link = element.find('a')
            href = link.get('href') if link else None
            
            return {
                "text": text_content,
                "link": href,
                "type": "stock"
            }
        except Exception as e:
            logger.debug(f"提取股票数据失败: {e}")
            return None
    
    async def _save_crawl_result(self, result: Dict[str, Any]):
        """保存爬取结果到文件"""
        try:
            # 保存到JSON文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"xueqiu_today_result_{timestamp}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            logger.info(f"结果已保存到文件: {filename}")

        except Exception as e:
            logger.error(f"保存结果失败: {e}")

async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='雪球今日页面深度爬取工具')
    parser.add_argument('--scroll-strategy', choices=['conservative', 'moderate', 'aggressive'],
                       default='aggressive', help='滚动策略')
    parser.add_argument('--max-scrolls', type=int, default=20, help='最大滚动次数')
    parser.add_argument('--disable-anti-detection', action='store_true', help='禁用反检测')
    
    args = parser.parse_args()
    
    # 创建爬虫实例
    crawler = XueqiuTodayCrawler(enable_anti_detection=not args.disable_anti_detection)
    
    # 开始爬取
    result = await crawler.crawl_today_page_deep(
        scroll_strategy=args.scroll_strategy,
        max_scrolls=args.max_scrolls
    )
    
    # 输出结果摘要
    print("\n" + "="*60)
    print("雪球今日页面爬取结果摘要")
    print("="*60)
    print(f"爬取状态: {'成功' if result['success'] else '失败'}")
    print(f"页面URL: {result['url']}")
    print(f"爬取时间: {result['crawl_time']}")
    print(f"响应时间: {result['response_time']:.2f}秒")
    print(f"滚动策略: {result.get('scroll_strategy', 'N/A')}")
    print(f"最大滚动次数: {result.get('max_scrolls', 'N/A')}")
    
    if result['success']:
        data = result['data']
        print(f"提取动态数量: {len(data.get('posts', []))}")
        print(f"提取话题数量: {len(data.get('hot_topics', []))}")
        print(f"提取股票数量: {len(data.get('stocks', []))}")
        print(f"总内容项目: {data.get('total_content_items', 0)}")
    else:
        print(f"错误信息: {result.get('error', 'Unknown error')}")
    
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
